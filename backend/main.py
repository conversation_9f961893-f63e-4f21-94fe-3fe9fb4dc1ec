from fastapi import FastAP<PERSON>, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import asyncio
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
import json
import random
import time

# 导入服务模块
from services.market_service import MarketService
from services.trading_service import TradingService
from services.strategy_service import StrategyService
from services.backtest_service import BacktestService
from services.user_service import UserService
from services.cache_service import CacheService
from models.response import APIResponse
from config.settings import get_settings

# 获取配置
settings = get_settings()

# 创建FastAPI应用
app = FastAPI(
    title="AI量化交易系统API",
    description="AI Trader V1.0 后端API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3088", "http://127.0.0.1:3088"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
market_service = MarketService()
trading_service = TradingService()
strategy_service = StrategyService()
backtest_service = BacktestService()
user_service = UserService()
cache_service = CacheService()

# ================================
# 健康检查接口
# ================================

@app.get("/health")
async def health_check():
    """健康检查"""
    return APIResponse.success({"status": "healthy", "timestamp": datetime.now().isoformat()})

# ================================
# 监控大屏 Dashboard API
# ================================

@app.get("/api/dashboard/overview")
async def get_dashboard_overview():
    """获取监控大屏概览数据"""
    try:
        # 获取全球指数数据
        global_indices = await market_service.get_global_indices()
        
        # 获取热门股票
        hot_stocks = await market_service.get_hot_stocks()
        
        # 获取商品期货数据
        commodities = await market_service.get_commodities_data()
        
        # 获取外汇数据
        forex = await market_service.get_forex_data()
        
        # 获取板块数据
        sectors = await market_service.get_sectors_data()

        # 获取市场统计
        market_stats = await market_service.get_market_statistics()
        
        # 获取风险监控指标
        risk_indicators = await market_service.get_risk_indicators()
        
        return APIResponse.success({
            "global_indices": global_indices,
            "hot_stocks": hot_stocks,
            "commodities": commodities,
            "forex": forex,
            "sectors": sectors,
            "market_stats": market_stats,
            "risk_indicators": risk_indicators,
            "update_time": datetime.now().isoformat()
        })
    except Exception as e:
        return APIResponse.error(f"获取监控大屏数据失败: {str(e)}")

@app.get("/api/dashboard/indices-trend")
async def get_indices_trend():
    """获取指数趋势图数据"""
    try:
        trend_data = await market_service.get_indices_trend_data()
        return APIResponse.success(trend_data)
    except Exception as e:
        return APIResponse.error(f"获取指数趋势数据失败: {str(e)}")

@app.get("/api/dashboard/sector-distribution")
async def get_sector_distribution():
    """获取板块分布数据"""
    try:
        distribution_data = await market_service.get_sector_distribution()
        return APIResponse.success(distribution_data)
    except Exception as e:
        return APIResponse.error(f"获取板块分布数据失败: {str(e)}")

# ================================
# 行情中心 Market API
# ================================

@app.get("/api/market/stocks")
async def get_stock_list(
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    sort_by: str = Query("code"),
    sort_order: str = Query("asc"),
    search: Optional[str] = None
):
    """获取股票列表"""
    try:
        stocks = await market_service.get_stock_list(page, size, sort_by, sort_order, search)
        return APIResponse.success(stocks)
    except Exception as e:
        return APIResponse.error(f"获取股票列表失败: {str(e)}")

@app.get("/api/market/stock/{symbol}")
async def get_stock_detail(symbol: str):
    """获取股票详情"""
    try:
        stock_detail = await market_service.get_stock_detail(symbol)
        return APIResponse.success(stock_detail)
    except Exception as e:
        return APIResponse.error(f"获取股票详情失败: {str(e)}")

@app.get("/api/market/realtime/{symbol}")
async def get_realtime_quote(symbol: str):
    """获取实时行情"""
    try:
        quote = await market_service.get_realtime_quote(symbol)
        return APIResponse.success(quote)
    except Exception as e:
        return APIResponse.error(f"获取实时行情失败: {str(e)}")

@app.get("/api/market/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", regex="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
    limit: int = Query(100, ge=1, le=1000),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """获取K线数据"""
    try:
        kline_data = await market_service.get_kline_data(symbol, period, limit, start_date, end_date)
        return APIResponse.success(kline_data)
    except Exception as e:
        return APIResponse.error(f"获取K线数据失败: {str(e)}")

@app.get("/api/market/indices")
async def get_indices():
    """获取指数数据"""
    try:
        indices = market_service.get_indices_data()
        return APIResponse.success(indices)
    except Exception as e:
        return APIResponse.error(f"获取指数数据失败: {str(e)}")

@app.get("/api/market/sectors")
async def get_sectors():
    """获取板块数据"""
    try:
        sectors = await market_service.get_sectors_data()
        return APIResponse.success(sectors)
    except Exception as e:
        return APIResponse.error(f"获取板块数据失败: {str(e)}")

@app.get("/api/market/hot-stocks")
async def get_hot_stocks(stock_type: str = Query("gainers", regex="^(gainers|losers|volume|turnover)$")):
    """获取热门股票排行"""
    try:
        hot_stocks = await market_service.get_hot_stocks_ranking()
        return APIResponse.success(hot_stocks)
    except Exception as e:
        return APIResponse.error(f"获取热门股票失败: {str(e)}")

@app.get("/api/market/popularity-ranking")
async def get_popularity_ranking(limit: int = Query(30, ge=1, le=50)):
    """获取人气榜前N名"""
    try:
        all_stocks = await market_service.get_hot_stocks_ranking()
        popularity_stocks = [stock for stock in all_stocks if stock.get('rank_type') == 'popularity']
        popularity_stocks = sorted(popularity_stocks, key=lambda x: x.get('rank', 999))[:limit]
        return APIResponse.success(popularity_stocks)
    except Exception as e:
        return APIResponse.error(f"获取人气榜失败: {str(e)}")

@app.get("/api/market/turnover-ranking")
async def get_turnover_ranking(limit: int = Query(30, ge=1, le=50)):
    """获取成交额榜前N名"""
    try:
        all_stocks = await market_service.get_hot_stocks_ranking()
        turnover_stocks = [stock for stock in all_stocks if stock.get('rank_type') == 'turnover']
        turnover_stocks = sorted(turnover_stocks, key=lambda x: x.get('rank', 999))[:limit]
        return APIResponse.success(turnover_stocks)
    except Exception as e:
        return APIResponse.error(f"获取成交额榜失败: {str(e)}")

@app.get("/api/market/commodities")
async def get_commodities():
    """获取商品期货数据"""
    try:
        commodities = await market_service.get_commodities_data()
        return APIResponse.success(commodities)
    except Exception as e:
        return APIResponse.error(f"获取商品期货数据失败: {str(e)}")

@app.get("/api/market/forex")
async def get_forex():
    """获取外汇数据"""
    try:
        forex = await market_service.get_forex_data()
        return APIResponse.success(forex)
    except Exception as e:
        return APIResponse.error(f"获取外汇数据失败: {str(e)}")

@app.get("/api/market/risk-indicators")
async def get_risk_indicators():
    """获取风险监控指标"""
    try:
        indicators = await market_service.get_risk_indicators()
        return APIResponse.success(indicators)
    except Exception as e:
        return APIResponse.error(f"获取风险指标失败: {str(e)}")

# ================================
# 交易中心 Trading API
# ================================

@app.post("/api/trading/order")
async def create_order(order_data: dict):
    """创建订单"""
    try:
        order = await trading_service.create_order(order_data)
        return APIResponse.success(order)
    except Exception as e:
        return APIResponse.error(f"创建订单失败: {str(e)}")

@app.get("/api/trading/orders")
async def get_orders(
    user_id: int = Query(...),
    status: Optional[str] = None,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取订单列表"""
    try:
        orders = await trading_service.get_orders(user_id, status, page, size)
        return APIResponse.success(orders)
    except Exception as e:
        return APIResponse.error(f"获取订单列表失败: {str(e)}")

@app.put("/api/trading/order/{order_id}/cancel")
async def cancel_order(order_id: int):
    """取消订单"""
    try:
        result = await trading_service.cancel_order(order_id)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"取消订单失败: {str(e)}")

@app.get("/api/trading/positions")
async def get_positions(user_id: int = Query(...)):
    """获取持仓信息"""
    try:
        positions = await trading_service.get_positions(user_id)
        return APIResponse.success(positions)
    except Exception as e:
        return APIResponse.error(f"获取持仓信息失败: {str(e)}")

@app.get("/api/trading/transactions")
async def get_transactions(
    user_id: int = Query(...),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取交易记录"""
    try:
        transactions = await trading_service.get_transactions(user_id, page, size)
        return APIResponse.success(transactions)
    except Exception as e:
        return APIResponse.error(f"获取交易记录失败: {str(e)}")

@app.get("/api/trading/account/{user_id}")
async def get_account_info(user_id: int):
    """获取账户信息"""
    try:
        account = await trading_service.get_account_info(user_id)
        return APIResponse.success(account)
    except Exception as e:
        return APIResponse.error(f"获取账户信息失败: {str(e)}")

@app.get("/api/trading/market-depth/{symbol}")
async def get_market_depth(symbol: str):
    """获取市场深度数据"""
    try:
        depth = await trading_service.get_market_depth(symbol)
        return APIResponse.success(depth)
    except Exception as e:
        return APIResponse.error(f"获取市场深度失败: {str(e)}")

# ================================
# 缓存 Cache API
# ================================

@app.get("/api/cache/indices")
async def get_cached_indices():
    """获取缓存的指数数据"""
    try:
        cached_data = await cache_service.get("indices_data")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无指数数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存指数数据失败: {str(e)}")

@app.get("/api/cache/hot-stocks")
async def get_cached_hot_stocks():
    """获取缓存的热门股票数据"""
    try:
        cached_data = await cache_service.get("hot_stocks_ranking_v6")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无热门股票数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存热门股票数据失败: {str(e)}")

@app.get("/api/cache/market-stats")
async def get_cached_market_stats():
    """获取缓存的市场统计数据"""
    try:
        cached_data = await cache_service.get("market_statistics")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无市场统计数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存市场统计数据失败: {str(e)}")

@app.get("/api/cache/sectors")
async def get_cached_sectors():
    """获取缓存的板块数据"""
    try:
        cached_data = await cache_service.get("sectors_data")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无板块数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存板块数据失败: {str(e)}")

@app.get("/api/cache/commodities")
async def get_cached_commodities():
    """获取缓存的商品期货数据"""
    try:
        cached_data = await cache_service.get("commodities_data")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无商品期货数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存商品期货数据失败: {str(e)}")

@app.get("/api/cache/forex")
async def get_cached_forex():
    """获取缓存的外汇数据"""
    try:
        cached_data = await cache_service.get("forex_data")
        if cached_data:
            return APIResponse.success(cached_data)
        else:
            return APIResponse.error("缓存中无外汇数据")
    except Exception as e:
        return APIResponse.error(f"获取缓存外汇数据失败: {str(e)}")

# ================================
# 策略中心 Strategy API
# ================================

@app.get("/api/strategy/list")
async def get_strategies(user_id: int = Query(...)):
    """获取用户策略列表"""
    try:
        strategies = await strategy_service.get_user_strategies(user_id)
        return APIResponse.success(strategies)
    except Exception as e:
        return APIResponse.error(f"获取策略列表失败: {str(e)}")

@app.post("/api/strategy/create")
async def create_strategy(strategy_data: dict):
    """创建新策略"""
    try:
        strategy = await strategy_service.create_strategy(strategy_data)
        return APIResponse.success(strategy)
    except Exception as e:
        return APIResponse.error(f"创建策略失败: {str(e)}")

@app.put("/api/strategy/{strategy_id}")
async def update_strategy(strategy_id: int, strategy_data: dict):
    """更新策略"""
    try:
        strategy = await strategy_service.update_strategy(strategy_id, strategy_data)
        return APIResponse.success(strategy)
    except Exception as e:
        return APIResponse.error(f"更新策略失败: {str(e)}")

@app.post("/api/strategy/{strategy_id}/start")
async def start_strategy(strategy_id: int):
    """启动策略"""
    try:
        result = await strategy_service.start_strategy(strategy_id)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"启动策略失败: {str(e)}")

@app.post("/api/strategy/{strategy_id}/stop")
async def stop_strategy(strategy_id: int):
    """停止策略"""
    try:
        result = await strategy_service.stop_strategy(strategy_id)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"停止策略失败: {str(e)}")

@app.get("/api/strategy/{strategy_id}/performance")
async def get_strategy_performance(strategy_id: int):
    """获取策略表现"""
    try:
        performance = await strategy_service.get_strategy_performance(strategy_id)
        return APIResponse.success(performance)
    except Exception as e:
        return APIResponse.error(f"获取策略表现失败: {str(e)}")

@app.get("/api/strategy/templates")
async def get_strategy_templates():
    """获取策略模板"""
    try:
        templates = await strategy_service.get_strategy_templates()
        return APIResponse.success(templates)
    except Exception as e:
        return APIResponse.error(f"获取策略模板失败: {str(e)}")

# ================================
# 回测中心 Backtest API
# ================================

@app.post("/api/backtest/create")
async def create_backtest(backtest_data: dict):
    """创建回测任务"""
    try:
        backtest = await backtest_service.create_backtest(backtest_data)
        return APIResponse.success(backtest)
    except Exception as e:
        return APIResponse.error(f"创建回测任务失败: {str(e)}")

@app.get("/api/backtest/list")
async def get_backtests(
    user_id: int = Query(...),
    status: Optional[str] = None,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取回测列表"""
    try:
        backtests = await backtest_service.get_backtests(user_id, status, page, size)
        return APIResponse.success(backtests)
    except Exception as e:
        return APIResponse.error(f"获取回测列表失败: {str(e)}")

@app.get("/api/backtest/{backtest_id}/result")
async def get_backtest_result(backtest_id: int):
    """获取回测结果"""
    try:
        result = await backtest_service.get_backtest_result(backtest_id)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"获取回测结果失败: {str(e)}")

@app.get("/api/backtest/{backtest_id}/analysis")
async def get_backtest_analysis(backtest_id: int):
    """获取详细回测分析"""
    try:
        analysis = await backtest_service.get_backtest_analysis(backtest_id)
        return APIResponse.success(analysis)
    except Exception as e:
        return APIResponse.error(f"获取回测分析失败: {str(e)}")

@app.delete("/api/backtest/{backtest_id}")
async def delete_backtest(backtest_id: int):
    """删除回测"""
    try:
        result = await backtest_service.delete_backtest(backtest_id)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"删除回测失败: {str(e)}")

@app.post("/api/backtest/compare")
async def compare_backtests(compare_data: dict):
    """对比回测结果"""
    try:
        comparison = await backtest_service.compare_backtests(compare_data)
        return APIResponse.success(comparison)
    except Exception as e:
        return APIResponse.error(f"回测对比失败: {str(e)}")

@app.get("/api/backtest/{backtest_id}/report")
async def get_backtest_report(backtest_id: int):
    """获取回测报告"""
    try:
        report = await backtest_service.get_backtest_report(backtest_id)
        return APIResponse.success(report)
    except Exception as e:
        return APIResponse.error(f"获取回测报告失败: {str(e)}")

@app.get("/api/market/stock-pool")
async def get_stock_pool():
    """获取股票池"""
    try:
        stock_pool = await market_service.get_stock_pool()
        return APIResponse.success(stock_pool)
    except Exception as e:
        return APIResponse.error(f"获取股票池失败: {str(e)}")

@app.get("/api/market/index-components/{index_code}")
async def get_index_components(index_code: str):
    """获取指数成分股"""
    try:
        components = await market_service.get_index_components(index_code)
        return APIResponse.success(components)
    except Exception as e:
        return APIResponse.error(f"获取指数成分股失败: {str(e)}")

# ================================
# 用户管理 User API
# ================================

@app.get("/api/user/profile")
async def get_user_profile(user_id: int = Query(...)):
    """获取用户信息"""
    try:
        profile = await user_service.get_user_profile(user_id)
        return APIResponse.success(profile)
    except Exception as e:
        return APIResponse.error(f"获取用户信息失败: {str(e)}")

@app.put("/api/user/profile")
async def update_user_profile(profile_data: dict):
    """更新用户信息"""
    try:
        profile = await user_service.update_user_profile(profile_data)
        return APIResponse.success(profile)
    except Exception as e:
        return APIResponse.error(f"更新用户信息失败: {str(e)}")

@app.get("/api/user/settings")
async def get_user_settings(user_id: int = Query(...)):
    """获取用户设置"""
    try:
        settings = await user_service.get_user_settings(user_id)
        return APIResponse.success(settings)
    except Exception as e:
        return APIResponse.error(f"获取用户设置失败: {str(e)}")

@app.put("/api/user/settings")
async def update_user_settings(settings_data: dict):
    """更新用户设置"""
    try:
        settings = await user_service.update_user_settings(settings_data)
        return APIResponse.success(settings)
    except Exception as e:
        return APIResponse.error(f"更新用户设置失败: {str(e)}")

@app.get("/api/user/watchlist")
async def get_user_watchlist(user_id: int = Query(...)):
    """获取用户自选股"""
    try:
        watchlist = await user_service.get_user_watchlist(user_id)
        return APIResponse.success(watchlist)
    except Exception as e:
        return APIResponse.error(f"获取自选股失败: {str(e)}")

@app.post("/api/user/watchlist")
async def add_to_watchlist(watchlist_data: dict):
    """添加自选股"""
    try:
        result = await user_service.add_to_watchlist(watchlist_data)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"添加自选股失败: {str(e)}")

@app.delete("/api/user/watchlist/{symbol}")
async def remove_from_watchlist(symbol: str, user_id: int = Query(...)):
    """移除自选股"""
    try:
        result = await user_service.remove_from_watchlist(user_id, symbol)
        return APIResponse.success(result)
    except Exception as e:
        return APIResponse.error(f"移除自选股失败: {str(e)}")

# ================================
# WebSocket 实时数据推送
# ================================

from fastapi import WebSocket, WebSocketDisconnect
import websockets

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[WebSocket, List[str]] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.subscriptions[websocket] = []

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.subscriptions:
            del self.subscriptions[websocket]

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_text(json.dumps(message))
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: dict):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                disconnected.append(connection)
        
        for connection in disconnected:
            self.disconnect(connection)

    async def send_to_subscribers(self, symbols: List[str], message: dict):
        disconnected = []
        for connection, subscribed_symbols in self.subscriptions.items():
            if any(symbol in subscribed_symbols for symbol in symbols):
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    disconnected.append(connection)
        
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()

@app.websocket("/ws/market")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("action") == "subscribe":
                symbols = message.get("symbols", [])
                manager.subscriptions[websocket] = symbols
                
                # 立即发送当前数据
                for symbol in symbols:
                    quote = await market_service.get_realtime_quote(symbol)
                    await manager.send_personal_message({
                        "type": "quote",
                        "symbol": symbol,
                        "data": quote
                    }, websocket)
            
            elif message.get("action") == "unsubscribe":
                symbols = message.get("symbols", [])
                current_subs = manager.subscriptions.get(websocket, [])
                manager.subscriptions[websocket] = [s for s in current_subs if s not in symbols]
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# 启动后台任务：实时数据推送
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    print("AI量化交易系统后端服务启动成功！")
    print(f"API文档地址: http://localhost:8000/docs")
    
    # 启动缓存预热
    await cache_service.warmup_cache()

async def real_time_data_pusher():
    """实时数据推送任务"""
    while True:
        try:
            # 获取热门股票列表
            hot_symbols = ["000001", "600519", "600036", "002594", "300750"]
            
            # 推送实时行情
            for symbol in hot_symbols:
                quote = await market_service.get_realtime_quote(symbol)
                await manager.send_to_subscribers([symbol], {
                    "type": "quote_update",
                    "symbol": symbol,
                    "data": quote,
                    "timestamp": datetime.now().isoformat()
                })
            
            # 推送市场统计
            market_stats = await market_service.get_market_statistics()
            await manager.broadcast({
                "type": "market_stats",
                "data": market_stats,
                "timestamp": datetime.now().isoformat()
            })
            
            await asyncio.sleep(3)  # 每3秒推送一次
            
        except Exception as e:
            print(f"实时数据推送错误: {e}")
            await asyncio.sleep(5)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 